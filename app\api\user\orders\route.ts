import { NextResponse } from "next/server";
import { getOrdersByUserId } from "@/src/db/orders";
import { createClient } from "@/utils/supabase/server";

export async function GET() {
  try {
    // Get current user
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const orders = await getOrdersByUserId(user.id);
    
    // Transform orders to match the expected format
    const transformedOrders = orders.map(order => ({
      id: order.id,
      date: order.createdAt,
      status: (order.status === 'pending' ? 'processing' : 
               order.status === 'refunded' ? 'cancelled' : 
               order.status) as 'processing' | 'shipped' | 'delivered' | 'cancelled',
      total: parseFloat(order.total),
      items: order.items.map(item => ({
        id: item.id,
        name: item.name,
        price: parseFloat(item.price),
        quantity: item.quantity,
        image: item.name.toLowerCase().includes('hoodie') ? '/assets/all-top.JPG' : '/assets/thin-logo.JPEG',
        size: item.description?.includes('/') ? item.description.split('/')[1]?.trim() : undefined,
        color: item.description?.includes('/') ? item.description.split('/')[0]?.trim() : undefined
      })),
      trackingNumber: order.status === 'shipped' || order.status === 'delivered' ? 
        `TRK${order.id.slice(0, 8).toUpperCase()}` : undefined
    }));
    
    return NextResponse.json(transformedOrders);
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to fetch orders" },
      { status: 500 }
    );
  }
}