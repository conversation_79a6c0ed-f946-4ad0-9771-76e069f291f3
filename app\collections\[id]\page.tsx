"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { SiteHeader } from "@/components/site-header";
import { SiteFooter } from "@/components/site-footer";
import { Button } from "@/components/ui/button";

// Collection item type
type CollectionItem = {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  category: string;
  items: CollectionProduct[];
};

type CollectionProduct = {
  id: string;
  name: string;
  price: string;
  imageUrl: string;
};

// Sample collection data with more details
const collectionsData: CollectionItem[] = [
  {
    id: "1",
    title: "Urban Essentials",
    description: "Versatile staples reimagined with a contemporary edge. Pieces that transition seamlessly from day to night, designed for the modern urban lifestyle.",
    imageUrl: "https://placehold.co/1200x600/111111/FFFFFF?text=Urban+Essentials",
    category: "Streetwear",
    items: [
      { id: "101", name: "Oversized Tee", price: "$0.50", imageUrl: "https://placehold.co/600x800/151515/FFFFFF?text=Oversized+Tee" },
      { id: "102", name: "Cargo Pants", price: "$89", imageUrl: "https://placehold.co/600x800/252525/FFFFFF?text=Cargo+Pants" },
      { id: "103", name: "Logo Hoodie", price: "$110", imageUrl: "https://placehold.co/600x800/353535/FFFFFF?text=Logo+Hoodie" },
      { id: "104", name: "Utility Jacket", price: "$165", imageUrl: "https://placehold.co/600x800/454545/FFFFFF?text=Utility+Jacket" }
    ]
  },
  {
    id: "2",
    title: "Minimalist Series",
    description: "Clean lines and understated elegance define our minimalist collection. High-quality basics with subtle design details that elevate everyday essentials.",
    imageUrl: "https://placehold.co/1200x600/222222/FFFFFF?text=Minimalist+Series",
    category: "Basics",
    items: [
      { id: "201", name: "Slim Fit Tee", price: "$40", imageUrl: "https://placehold.co/600x800/151515/FFFFFF?text=Slim+Fit+Tee" },
      { id: "202", name: "Classic Denim", price: "$95", imageUrl: "https://placehold.co/600x800/252525/FFFFFF?text=Classic+Denim" },
      { id: "203", name: "Lightweight Sweater", price: "$75", imageUrl: "https://placehold.co/600x800/353535/FFFFFF?text=Lightweight+Sweater" },
      { id: "204", name: "Shirt Jacket", price: "$120", imageUrl: "https://placehold.co/600x800/454545/FFFFFF?text=Shirt+Jacket" }
    ]
  },
  {
    id: "3",
    title: "Bold Statements",
    description: "Make an impact with our collection of graphic-driven designs. Featuring original artwork and striking prints that reflect our creative vision.",
    imageUrl: "https://placehold.co/1200x600/333333/FFFFFF?text=Bold+Statements",
    category: "Graphic",
    items: [
      { id: "301", name: "Statement Tee", price: "$50", imageUrl: "https://placehold.co/600x800/151515/FFFFFF?text=Statement+Tee" },
      { id: "302", name: "Graphic Hoodie", price: "$110", imageUrl: "https://placehold.co/600x800/252525/FFFFFF?text=Graphic+Hoodie" },
      { id: "303", name: "Print Overshirt", price: "$95", imageUrl: "https://placehold.co/600x800/353535/FFFFFF?text=Print+Overshirt" },
      { id: "304", name: "Embroidered Sweatshirt", price: "$85", imageUrl: "https://placehold.co/600x800/454545/FFFFFF?text=Embroidered+Sweatshirt" }
    ]
  },
  {
    id: "4",
    title: "Seasonal Layers",
    description: "Thoughtfully designed outerwear that combines functionality with forward-thinking design. Adaptable pieces that respond to changing conditions.",
    imageUrl: "https://placehold.co/1200x600/444444/FFFFFF?text=Seasonal+Layers",
    category: "Outerwear",
    items: [
      { id: "401", name: "Technical Jacket", price: "$180", imageUrl: "https://placehold.co/600x800/151515/FFFFFF?text=Technical+Jacket" },
      { id: "402", name: "Puffer Vest", price: "$125", imageUrl: "https://placehold.co/600x800/252525/FFFFFF?text=Puffer+Vest" },
      { id: "403", name: "Rain Shell", price: "$150", imageUrl: "https://placehold.co/600x800/353535/FFFFFF?text=Rain+Shell" },
      { id: "404", name: "Wool Overshirt", price: "$145", imageUrl: "https://placehold.co/600x800/454545/FFFFFF?text=Wool+Overshirt" }
    ]
  },
  {
    id: "5",
    title: "Athleisure Mix",
    description: "The intersection of performance and style. Technical fabrics and ergonomic cuts designed for movement, comfort, and a contemporary aesthetic.",
    imageUrl: "https://placehold.co/1200x600/555555/FFFFFF?text=Athleisure+Mix",
    category: "Active",
    items: [
      { id: "501", name: "Performance Tee", price: "$55", imageUrl: "https://placehold.co/600x800/151515/FFFFFF?text=Performance+Tee" },
      { id: "502", name: "Track Pants", price: "$90", imageUrl: "https://placehold.co/600x800/252525/FFFFFF?text=Track+Pants" },
      { id: "503", name: "Technical Hoodie", price: "$120", imageUrl: "https://placehold.co/600x800/353535/FFFFFF?text=Technical+Hoodie" },
      { id: "504", name: "Lightweight Shell", price: "$130", imageUrl: "https://placehold.co/600x800/454545/FFFFFF?text=Lightweight+Shell" }
    ]
  },
  {
    id: "6",
    title: "Evening Elements",
    description: "Elevated essentials for after-hours style. Premium materials and tailored fits that transition smoothly from formal settings to evening occasions.",
    imageUrl: "https://placehold.co/1200x600/666666/FFFFFF?text=Evening+Elements",
    category: "Formal",
    items: [
      { id: "601", name: "Dress Shirt", price: "$95", imageUrl: "https://placehold.co/600x800/151515/FFFFFF?text=Dress+Shirt" },
      { id: "602", name: "Slim Trousers", price: "$110", imageUrl: "https://placehold.co/600x800/252525/FFFFFF?text=Slim+Trousers" },
      { id: "603", name: "Tailored Jacket", price: "$225", imageUrl: "https://placehold.co/600x800/353535/FFFFFF?text=Tailored+Jacket" },
      { id: "604", name: "Evening Coat", price: "$280", imageUrl: "https://placehold.co/600x800/454545/FFFFFF?text=Evening+Coat" }
    ]
  },
  {
    id: "7",
    title: "Tonal Textures",
    description: "A study in subtle contrasts. Monochromatic pieces with varying textures and materials that create depth and visual interest through tactile elements.",
    imageUrl: "https://placehold.co/1200x600/777777/FFFFFF?text=Tonal+Textures",
    category: "Streetwear",
    items: [
      { id: "701", name: "Textured Tee", price: "$50", imageUrl: "https://placehold.co/600x800/151515/FFFFFF?text=Textured+Tee" },
      { id: "702", name: "Knit Pants", price: "$95", imageUrl: "https://placehold.co/600x800/252525/FFFFFF?text=Knit+Pants" },
      { id: "703", name: "Boucle Overshirt", price: "$125", imageUrl: "https://placehold.co/600x800/353535/FFFFFF?text=Boucle+Overshirt" },
      { id: "704", name: "Contrast Hoodie", price: "$115", imageUrl: "https://placehold.co/600x800/454545/FFFFFF?text=Contrast+Hoodie" }
    ]
  },
  {
    id: "8",
    title: "Contrast Classics",
    description: "Traditional silhouettes reimagined with contemporary details. Familiar forms with unexpected elements that challenge conventional style boundaries.",
    imageUrl: "https://placehold.co/1200x600/888888/FFFFFF?text=Contrast+Classics",
    category: "Basics",
    items: [
      { id: "801", name: "Split Hem Tee", price: "$45", imageUrl: "https://placehold.co/600x800/151515/FFFFFF?text=Split+Hem+Tee" },
      { id: "802", name: "Cropped Chinos", price: "$85", imageUrl: "https://placehold.co/600x800/252525/FFFFFF?text=Cropped+Chinos" },
      { id: "803", name: "Deconstructed Shirt", price: "$95", imageUrl: "https://placehold.co/600x800/353535/FFFFFF?text=Deconstructed+Shirt" },
      { id: "804", name: "Structured Sweatshirt", price: "$80", imageUrl: "https://placehold.co/600x800/454545/FFFFFF?text=Structured+Sweatshirt" }
    ]
  },
  {
    id: "9",
    title: "Monochrome Moods",
    description: "A focused palette that emphasizes form and silhouette. Black, white, and grayscale pieces that create a cohesive wardrobe foundation.",
    imageUrl: "https://placehold.co/1200x600/999999/FFFFFF?text=Monochrome+Moods",
    category: "Minimalist",
    items: [
      { id: "901", name: "Essential Tee", price: "$40", imageUrl: "https://placehold.co/600x800/151515/FFFFFF?text=Essential+Tee" },
      { id: "902", name: "Relaxed Pants", price: "$95", imageUrl: "https://placehold.co/600x800/252525/FFFFFF?text=Relaxed+Pants" },
      { id: "903", name: "Minimal Sweatshirt", price: "$85", imageUrl: "https://placehold.co/600x800/353535/FFFFFF?text=Minimal+Sweatshirt" },
      { id: "904", name: "Oversized Shirt", price: "$90", imageUrl: "https://placehold.co/600x800/454545/FFFFFF?text=Oversized+Shirt" }
    ]
  },
];

export default function CollectionDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [collection, setCollection] = useState<CollectionItem | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const [activeImageIndex, setActiveImageIndex] = useState(0);
  const [galleryView, setGalleryView] = useState(false);
  
  useEffect(() => {
    // Simulate loading collection data
    setLoading(true);
    
    const collectionId = params.id as string;
    const foundCollection = collectionsData.find(item => item.id === collectionId);
    
    if (foundCollection) {
      setCollection(foundCollection);
      setLoading(false);
    } else {
      // Collection not found, redirect to collections page
      router.push('/collections');
    }
  }, [params.id, router]);
  
  if (loading) {
    return (
      <div className="min-h-screen flex flex-col bg-neutral-900 text-white">
        <SiteHeader />
        <main className="flex-grow flex items-center justify-center">
          <div className="text-center">
            <div className="w-12 h-12 border-t-2 border-white rounded-full animate-spin mx-auto mb-4"></div>
            <p>Loading collection...</p>
          </div>
        </main>
        <SiteFooter />
      </div>
    );
  }
  
  if (!collection) {
    return null; // This should not happen due to the redirect
  }
  
  // Function to handle gallery navigation
  const navigateGallery = (direction: 'next' | 'prev') => {
    if (direction === 'next') {
      setActiveImageIndex((prev) => 
        prev === collection.items.length - 1 ? 0 : prev + 1
      );
    } else {
      setActiveImageIndex((prev) => 
        prev === 0 ? collection.items.length - 1 : prev - 1
      );
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-neutral-900 text-white">
      <SiteHeader />
      
      <main className="flex-grow py-12">
        {/* Collection Header */}
        <div className="relative h-[50vh] mb-12">
          <Image
            src={collection.imageUrl}
            alt={collection.title}
            fill
            className="object-cover"
            sizes="100vw"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent/20 flex items-end">
            <div className="container mx-auto px-4 md:px-6 py-12">
              <div className="max-w-4xl">
                <h1 className="text-4xl md:text-6xl font-bold mb-4">{collection.title}</h1>
                <p className="text-xl text-gray-200 max-w-2xl">{collection.description}</p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="container mx-auto px-4 md:px-6">
          {/* Breadcrumb */}
          <div className="mb-8">
            <nav className="flex text-sm">
              <Link href="/" className="text-gray-400 hover:text-white">Home</Link>
              <span className="mx-2 text-gray-500">/</span>
              <Link href="/collections" className="text-gray-400 hover:text-white">Collections</Link>
              <span className="mx-2 text-gray-500">/</span>
              <span className="text-white">{collection.title}</span>
            </nav>
          </div>
          
          {/* Gallery/Grid View Toggle */}
          <div className="flex justify-end mb-8">
            <div className="flex items-center space-x-2 border border-neutral-700 rounded-md p-1">
              <button 
                className={`p-2 rounded ${!galleryView ? 'bg-white text-black' : 'text-white'}`}
                onClick={() => setGalleryView(false)}
                aria-label="Grid view"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="3" y="3" width="7" height="7" />
                  <rect x="14" y="3" width="7" height="7" />
                  <rect x="14" y="14" width="7" height="7" />
                  <rect x="3" y="14" width="7" height="7" />
                </svg>
              </button>
              <button 
                className={`p-2 rounded ${galleryView ? 'bg-white text-black' : 'text-white'}`}
                onClick={() => setGalleryView(true)}
                aria-label="Gallery view"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="2" y="2" width="20" height="20" rx="5" />
                  <circle cx="8.5" cy="8.5" r="1.5" />
                  <path d="M20.4 14.5L16 10 4 20" />
                </svg>
              </button>
            </div>
          </div>
          
          {/* Collection Products */}
          {galleryView ? (
            // Gallery View
            <div className="relative bg-neutral-800 rounded-lg overflow-hidden">
              <div className="aspect-[4/3] relative">
                <Image
                  src={collection.items[activeImageIndex].imageUrl}
                  alt={collection.items[activeImageIndex].name}
                  fill
                  className="object-contain"
                  sizes="(max-width: 768px) 100vw, 80vw"
                />
                
                {/* Navigation Controls */}
                <button 
                  className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 p-3 rounded-full"
                  onClick={() => navigateGallery('prev')}
                  aria-label="Previous image"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="15 18 9 12 15 6" />
                  </svg>
                </button>
                <button 
                  className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 p-3 rounded-full"
                  onClick={() => navigateGallery('next')}
                  aria-label="Next image"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="9 18 15 12 9 6" />
                  </svg>
                </button>
              </div>
              
              {/* Thumbnails */}
              <div className="p-4 border-t border-neutral-700">
                <div className="flex overflow-x-auto gap-2 py-2 scrollbar-thin scrollbar-thumb-neutral-700">
                  {collection.items.map((item, index) => (
                    <button 
                      key={item.id}
                      className={`flex-none w-20 h-20 relative rounded overflow-hidden border ${
                        index === activeImageIndex ? 'border-white' : 'border-transparent'
                      }`}
                      onClick={() => setActiveImageIndex(index)}
                    >
                      <Image
                        src={item.imageUrl}
                        alt={item.name}
                        fill
                        className="object-cover"
                        sizes="80px"
                      />
                    </button>
                  ))}
                </div>
                
                <div className="mt-4 flex justify-between items-center">
                  <div>
                    <h3 className="text-xl font-medium">{collection.items[activeImageIndex].name}</h3>
                    <p className="text-gray-300">{collection.items[activeImageIndex].price}</p>
                  </div>
                  <Link href={`/products/${collection.items[activeImageIndex].id}`}>
                    <Button className="rounded-none bg-white text-black hover:bg-gray-200">
                      View Details
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          ) : (
            // Grid View - Products
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {collection.items.map((item) => (
                <Link href={`/products/${item.id}`} key={item.id} className="group">
                  <div className="relative aspect-[3/4] overflow-hidden bg-neutral-800">
                    <Image
                      src={item.imageUrl}
                      alt={item.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-500"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-4">
                      <Button variant="outline" className="w-full rounded-none border-white/70 text-white bg-transparent hover:bg-white hover:text-black">
                        Quick View
                      </Button>
                    </div>
                  </div>
                  <div className="mt-4">
                    <h3 className="text-lg font-medium">{item.name}</h3>
                    <p className="text-gray-300">{item.price}</p>
                  </div>
                </Link>
              ))}
            </div>
          )}
          
          {/* Collection Story */}
          <div className="mt-24 mb-16">
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl font-bold mb-6">The Story</h2>
              <p className="text-lg text-gray-300 mb-8">
                {collection.description} Our design team created this collection drawing inspiration from urban landscapes and the interplay between structure and movement. Each piece is crafted to combine functionality with distinctive design elements that reflect our aesthetic vision.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-12">
                <div className="bg-neutral-800 p-6 flex flex-col items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mb-4">
                    <path d="M20.38 3.46 16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z" />
                  </svg>
                  <h3 className="text-lg font-medium mb-2">Premium Materials</h3>
                  <p className="text-center text-gray-400">Carefully selected fabrics for comfort and durability</p>
                </div>
                <div className="bg-neutral-800 p-6 flex flex-col items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mb-4">
                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10" />
                  </svg>
                  <h3 className="text-lg font-medium mb-2">Ethical Production</h3>
                  <p className="text-center text-gray-400">Responsibly manufactured with attention to working conditions</p>
                </div>
                <div className="bg-neutral-800 p-6 flex flex-col items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mb-4">
                    <path d="M3 6v18h18" />
                    <path d="m19 6-5.07 5.07a5 5 0 0 1-7.07 0L2 6" />
                  </svg>
                  <h3 className="text-lg font-medium mb-2">Distinctive Design</h3>
                  <p className="text-center text-gray-400">Unique details that set our pieces apart</p>
                </div>
              </div>
            </div>
          </div>
          
          {/* Related Collections */}
          <div className="mt-24">
            <h2 className="text-2xl font-bold mb-8">You May Also Like</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {collectionsData
                .filter(item => item.id !== collection.id)
                .slice(0, 3)
                .map((relatedCollection) => (
                  <Link 
                    href={`/collections/${relatedCollection.id}`} 
                    key={relatedCollection.id}
                    className="group"
                  >
                    <div className="relative aspect-[3/2] overflow-hidden bg-neutral-800">
                      <Image
                        src={relatedCollection.imageUrl}
                        alt={relatedCollection.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-500"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 33vw, 25vw"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end p-6">
                        <div>
                          <h3 className="text-xl font-medium mb-1">{relatedCollection.title}</h3>
                          <p className="text-sm text-gray-300">{relatedCollection.category}</p>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
            </div>
          </div>
        </div>
      </main>
      
      <SiteFooter />
    </div>
  );
}