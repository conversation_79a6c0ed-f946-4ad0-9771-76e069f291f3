import { But<PERSON> } from "@/components/ui/button";
import { SiteHeader } from "@/components/site-header";
import { SiteFooter } from "@/components/site-footer";
import { ProductCard } from "@/components/product-card";

// Animation styles for the line drawing and title effects
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const animationStyles = {
  lineDraw: `
    @keyframes lineDraw {
      0% { 
        stroke-dashoffset: 1000; 
        opacity: 0.1;
      }
      40% { 
        stroke-dashoffset: 0; 
        opacity: 0.2;
      }
      60% { 
        stroke-dashoffset: 0; 
        opacity: 0.2;
      }
      100% { 
        stroke-dashoffset: -1000; 
        opacity: 0.1;
      }
    }
    
    .line-draw {
      animation: lineDraw 5s ease-in-out infinite;
      opacity: 0.1;
    }
    
    .line-draw-delayed {
      animation: lineDraw 5s ease-in-out 1.5s infinite;
      opacity: 0.1;
    }
    
    .line-draw-delayed-more {
      animation: lineDraw 5s ease-in-out 3s infinite;
      opacity: 0.1;
    }
    
    .revision-grid {
      position: absolute;
      inset: 0;
      width: 100%;
      height: 100%;
      background-image: linear-gradient(to right, rgba(0,0,0,0.05) 1px, transparent 1px),
                        linear-gradient(to bottom, rgba(0,0,0,0.05) 1px, transparent 1px);
      background-size: 20px 20px;
      opacity: 0.2;
    }
  `,
  
  colorShiftBlue: `
        @keyframes colorShiftBlue {
          0%, 85%, 100% {
            color: black;
            text-shadow: none;
          }
          10%, 25% {
            color: #3b82f6; /* Blue */
            text-shadow: 0 0 8px rgba(59, 130, 246, 0.7); /* Subtle blue glow */
          }
        }
        
        .letter {
          display: inline-block;
        }
        
        /* Apply the same synchronized animation to R, E, V, S, N */
        .letter:nth-child(1),
        .letter:nth-child(2),
        .letter:nth-child(3),
        .letter:nth-child(5),
        .letter:nth-child(8) {
          animation: colorShiftBlue 3s infinite;
        }`
}

export default function Home() {
  const featuredProducts = [
    { 
      id: "cropped-hoodie", 
      name: "Cropped Zip Up Hoodie", 
      price: "$0.50", 
      category: "Outerwear", 
      isNew: true, 
      imageSrc: "/assets/all-top.JPG",
      imageAlt: "Cropped Zip Up Hoodie in multiple colors"
    }
  ];

  return (
    <div className="min-h-screen flex flex-col bg-neutral-900 text-white">
      <SiteHeader />
      
      {/* Hero Section */}
      <section className="relative min-h-[60vh] md:min-h-[70vh] flex items-center justify-center overflow-hidden py-10 md:py-0">
        {/* Line redrawing background animation - replace with dark version */}
        <div className="absolute inset-0 w-full h-full pointer-events-none">
          <style dangerouslySetInnerHTML={{ __html: `
            @keyframes lineDraw {
              0% { 
                stroke-dashoffset: 1000; 
                opacity: 0.15;
              }
              40% { 
                stroke-dashoffset: 0; 
                opacity: 0.25;
              }
              60% { 
                stroke-dashoffset: 0; 
                opacity: 0.25;
              }
              100% { 
                stroke-dashoffset: -1000; 
                opacity: 0.15;
              }
            }
            
            .line-draw {
              animation: lineDraw 5s ease-in-out infinite;
              opacity: 0.15;
            }
            
            .line-draw-delayed {
              animation: lineDraw 5s ease-in-out 1.5s infinite;
              opacity: 0.15;
            }
            
            .line-draw-delayed-more {
              animation: lineDraw 5s ease-in-out 3s infinite;
              opacity: 0.15;
            }
            
            .revision-grid {
              position: absolute;
              inset: 0;
              width: 100%;
              height: 100%;
              background-image: linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px),
                                linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px);
              background-size: 20px 20px;
              opacity: 0.15;
            }
          `}} />
          
          <svg className="absolute inset-0 w-full h-full" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000" preserveAspectRatio="none">
            {/* Horizontal lines */}
            <line 
              x1="0" y1="250" x2="1000" y2="250" 
              stroke="white" strokeWidth="0.5" strokeDasharray="1000" 
              className="line-draw"
            />
            <line 
              x1="0" y1="500" x2="1000" y2="500" 
              stroke="white" strokeWidth="0.5" strokeDasharray="1000" 
              className="line-draw-delayed"
            />
            <line 
              x1="0" y1="750" x2="1000" y2="750" 
              stroke="white" strokeWidth="0.5" strokeDasharray="1000" 
              className="line-draw-delayed-more"
            />
            
            {/* Vertical lines */}
            <line 
              x1="250" y1="0" x2="250" y2="1000" 
              stroke="white" strokeWidth="0.5" strokeDasharray="1000" 
              className="line-draw"
            />
            <line 
              x1="500" y1="0" x2="500" y2="1000" 
              stroke="white" strokeWidth="0.5" strokeDasharray="1000" 
              className="line-draw-delayed"
            />
            <line 
              x1="750" y1="0" x2="750" y2="1000" 
              stroke="white" strokeWidth="0.5" strokeDasharray="1000" 
              className="line-draw-delayed-more"
            />
            
            {/* Diagonal lines */}
            <line 
              x1="0" y1="0" x2="1000" y2="1000" 
              stroke="white" strokeWidth="0.5" strokeDasharray="1414" 
              className="line-draw"
            />
            <line 
              x1="1000" y1="0" x2="0" y2="1000" 
              stroke="white" strokeWidth="0.5" strokeDasharray="1414" 
              className="line-draw-delayed"
            />
            
            {/* Curved lines representing revision/editing */}
            <path 
              d="M200,300 Q400,100 600,300 T1000,300" 
              fill="none" stroke="white" strokeWidth="0.5" strokeDasharray="1000" 
              className="line-draw"
            />
            <path 
              d="M0,700 Q200,900 400,700 T800,700" 
              fill="none" stroke="white" strokeWidth="0.5" strokeDasharray="1000" 
              className="line-draw-delayed"
            />
          </svg>
          
          {/* Grid pattern using CSS instead of SVG */}
          <div className="revision-grid"></div>
        </div>

        <div className="relative z-10 text-center px-4 sm:px-6 md:px-16 max-w-5xl mx-auto w-full">
          <h2 className="text-sm tracking-[0.2em] text-gray-400 mb-3 font-light">EST 2023</h2>
          <div className="space-y-6 md:space-y-8">
            <div className="title-container">
              <style dangerouslySetInnerHTML={{ __html: `
                @keyframes colorShiftBlue {
                  0%, 85%, 100% {
                    color: white;
                    text-shadow: none;
                  }
                  10%, 25% {
                    color: #60a5fa; /* Lighter blue for dark theme */
                    text-shadow: 0 0 8px rgba(96, 165, 250, 0.7); /* Blue glow */
                  }
                }
                
                .letter {
                  display: inline-block;
                }
                
                /* Apply the same synchronized animation to R, E, V, S, N */
                .letter:nth-child(1),
                .letter:nth-child(2),
                .letter:nth-child(3),
                .letter:nth-child(5),
                .letter:nth-child(8) {
                  animation: colorShiftBlue 3s infinite;
                }
                
                /* Mobile responsiveness fixes */
                @media (max-width: 640px) {
                  .title-container h1 {
                    font-size: 5rem;
                    line-height: 1;
                    letter-spacing: -0.05em;
                    white-space: nowrap;
                  }
                }
              `}} />
              
              <h1 className="text-8xl md:text-[9rem] font-bold tracking-tighter leading-[0.8] mb-6 text-white">
                <span className="letter">R</span>
                <span className="letter">E</span>
                <span className="letter">V</span>
                <span className="letter">I</span>
                <span className="letter">S</span>
                <span className="letter">I</span>
                <span className="letter">O</span>
                <span className="letter">N</span>
              </h1>
            </div>
            <p className="text-2xl sm:text-3xl md:text-5xl font-extralight tracking-tight relative group">
              <span className="bg-gradient-to-r from-white to-white bg-[length:0%_1px] hover:bg-[length:100%_1px] bg-no-repeat bg-bottom transition-all duration-500">REVISED OUTLOOK</span>
            </p>
            <div className="h-[1px] w-20 bg-white mx-auto my-6 md:my-8 group-hover:w-40 transition-all duration-500"></div>
            <p className="text-gray-300 text-base sm:text-lg max-w-lg mx-auto">
              Timeless clothing for the modern wardrobe. Designed with you in mind.
            </p>
          </div>
        </div>
      </section>

      {/* Brand Values */}
      <section className="py-20 md:py-32 px-4 sm:px-6 md:px-16 bg-neutral-800">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-12 md:gap-16 lg:gap-24">
            <div className="text-center relative group hover:-translate-y-1 transition-transform duration-300">
              <div className="absolute -top-8 left-1/2 -translate-x-1/2 h-[1px] w-12 bg-gray-300 group-hover:w-20 transition-all duration-300"></div>
              <div className="mb-8 inline-flex h-16 sm:h-20 w-16 sm:w-20 items-center justify-center rounded-full border border-gray-300 text-white group-hover:border-white group-hover:border-2 transition-all duration-300">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="32"
                  height="32"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="group-hover:scale-110 transition-transform duration-300 h-6 w-6 sm:h-8 sm:w-8"
                >
                  <path d="M20.38 3.46 16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z" />
                </svg>
              </div>
              <h3 className="text-xl sm:text-2xl font-medium mb-3 sm:mb-4 text-white group-hover:text-gray-300 transition-colors duration-300">Sustainable Materials</h3>
              <p className="text-gray-300 text-base sm:text-lg">
                Ethically sourced fabrics and materials selected for quality and longevity.
              </p>
            </div>
            <div className="text-center relative group hover:-translate-y-1 transition-transform duration-300">
              <div className="absolute -top-8 left-1/2 -translate-x-1/2 h-[1px] w-12 bg-gray-300 group-hover:w-20 transition-all duration-300"></div>
              <div className="mb-8 inline-flex h-16 sm:h-20 w-16 sm:w-20 items-center justify-center rounded-full border border-gray-300 text-white group-hover:border-white group-hover:border-2 transition-all duration-300">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="32"
                  height="32"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="group-hover:scale-110 transition-transform duration-300 h-6 w-6 sm:h-8 sm:w-8"
                >
                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10" />
                </svg>
              </div>
              <h3 className="text-xl sm:text-2xl font-medium mb-3 sm:mb-4 text-white group-hover:text-gray-300 transition-colors duration-300">Ethical Production</h3>
              <p className="text-gray-300 text-base sm:text-lg">
                Fair labor practices and transparent manufacturing processes in small-batch production.
              </p>
            </div>
            <div className="text-center relative group hover:-translate-y-1 transition-transform duration-300">
              <div className="absolute -top-8 left-1/2 -translate-x-1/2 h-[1px] w-12 bg-gray-300 group-hover:w-20 transition-all duration-300"></div>
              <div className="mb-8 inline-flex h-16 sm:h-20 w-16 sm:w-20 items-center justify-center rounded-full border border-gray-300 text-white group-hover:border-white group-hover:border-2 transition-all duration-300">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="32"
                  height="32"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="group-hover:scale-110 transition-transform duration-300 h-6 w-6 sm:h-8 sm:w-8"
                >
                  <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z" />
                </svg>
              </div>
              <h3 className="text-xl sm:text-2xl font-medium mb-3 sm:mb-4 text-white group-hover:text-gray-300 transition-colors duration-300">Timeless Design</h3>
              <p className="text-gray-300 text-base sm:text-lg">
                Enduring aesthetics that transcend seasonal trends, focusing on versatility and wearability.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section id="shop" className="py-20 md:py-32 px-4 sm:px-6 md:px-16 bg-neutral-900">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-12 md:mb-20">
            <div className="relative">
              <div className="absolute -top-8 left-0 h-[1px] w-12 bg-white"></div>
              <h2 className="text-3xl sm:text-4xl md:text-5xl font-semibold tracking-tight relative inline-block text-white">
                Featured Products
                <span className="absolute -bottom-2 left-0 w-0 h-[2px] bg-white group-hover:w-full transition-all duration-500"></span>
              </h2>
              <p className="text-gray-300 mt-3 md:mt-4 text-base sm:text-lg">Discover our most sought-after pieces.</p>
            </div>
            <Button variant="link" className="text-gray-300 hover:text-white mt-4 md:mt-0 p-0 h-auto hover:no-underline text-base sm:text-lg group">
              <span className="group-hover:translate-x-2 transition-transform duration-300">View All Collection</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="ml-2 h-4 w-4 group-hover:translate-x-2 transition-transform duration-300"
              >
                <path d="M5 12h14" />
                <path d="m12 5 7 7-7 7" />
              </svg>
            </Button>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 md:gap-10">
            {featuredProducts.map((product) => (
              <ProductCard key={product.id} {...product} />
            ))}
          </div>
        </div>
      </section>

      <SiteFooter />
    </div>
  );
} 