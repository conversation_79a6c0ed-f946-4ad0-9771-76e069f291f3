import Link from "next/link";
import Image from "next/image";
import { cn } from "@/lib/utils";

interface ProductCardProps {
  id: string;
  name: string;
  price: string;
  imagePlaceholder?: boolean;
  imageSrc?: string;
  imageAlt?: string;
  category?: string;
  isNew?: boolean;
  className?: string;
}

export function ProductCard({
  id,
  name,
  price,
  imagePlaceholder = true,
  imageSrc,
  imageAlt,
  category,
  isNew = false,
  className,
}: ProductCardProps) {
  return (
    <div className={cn("group relative transition-all duration-300 hover:-translate-y-1 border border-gray-700 p-3 text-white", className)}>
      <div className="aspect-[3/4] w-full overflow-hidden bg-neutral-800 relative">
        {imageSrc ? (
          <Image
            src={imageSrc}
            alt={imageAlt || name}
            width={500}
            height={667}
            className="h-full w-full object-cover object-center transition-transform duration-500 group-hover:scale-105"
          />
        ) : imagePlaceholder ? (
          <div className="flex h-full w-full items-center justify-center text-gray-400">
            <span className="text-xs uppercase tracking-wider">Image Placeholder</span>
          </div>
        ) : null}
        
        {isNew && (
          <div className="absolute top-2 left-2 bg-gray-700 px-2 py-1 z-10">
            <span className="text-xs font-medium uppercase tracking-wider text-white">New</span>
          </div>
        )}
        
        {/* Overlay with View Details button only */}
        <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col items-center justify-center gap-3 p-4">
          <Link 
            href={`/products/${id}`} 
            className="w-4/5 border border-white py-3 text-center text-xs font-medium uppercase tracking-wider text-white hover:bg-white/20 transition-colors duration-200"
          >
            View Details
          </Link>
        </div>
      </div>
      
      <div className="mt-4 flex items-center justify-between">
        <div>
          <h3 className="text-sm font-medium text-white group-hover:underline underline-offset-4 transition-all duration-200">{name}</h3>
          {category && (
            <p className="mt-1 text-xs text-gray-400">{category}</p>
          )}
        </div>
        <p className="text-sm font-medium text-white">{price}</p>
      </div>
    </div>
  );
} 