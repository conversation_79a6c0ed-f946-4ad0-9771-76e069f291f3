// Export Supabase client utilities
// Re-export the client creation functions for flexibility
export { createClient as createClientBrowser } from '@/utils/supabase/client'
export { createClient as createClientServer } from '@/utils/supabase/server'
export { updateSession } from '@/utils/supabase/middleware'

// Create a function to get a fresh client instance instead of a singleton
export function getSupabaseClient() {
  return createClientBrowser();
}

// Default export for convenience - use function instead of singleton
export default getSupabaseClient;
