import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/src/db/auth';
import { db } from '@/src/db/index';
import { inventory, products, productVariants, colors, sizes } from '@/src/db/schema';
import { eq, and, isNull, isNotNull } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    // Require admin access
    await requireAdmin();

    console.log('🔧 Admin-initiated inventory fix for Cropped Zip Up Hoodie...');

    // 1. Find the product
    const product = await db
      .select()
      .from(products)
      .where(eq(products.name, 'Cropped Zip Up Hoodie'))
      .limit(1);

    if (!product || product.length === 0) {
      return NextResponse.json({ error: 'Product "Cropped Zip Up Hoodie" not found' }, { status: 404 });
    }

    // 2. Find all variants that have both colorId and sizeId (proper variants)
    const properVariants = await db
      .select({
        variantId: productVariants.id,
        productId: productVariants.productId,
        colorId: productVariants.colorId,
        sizeId: productVariants.sizeId,
        colorName: colors.name,
        sizeName: sizes.name,
        sku: productVariants.sku
      })
      .from(productVariants)
      .leftJoin(colors, eq(productVariants.colorId, colors.id))
      .leftJoin(sizes, eq(productVariants.sizeId, sizes.id))
      .where(
        and(
          eq(productVariants.productId, product[0].id),
          isNotNull(productVariants.colorId),
          isNotNull(productVariants.sizeId)
        )
      );

    // 3. Find variants without inventory records
    const variantsWithoutInventory = [];
    
    for (const variant of properVariants) {
      const existingInventory = await db
        .select()
        .from(inventory)
        .where(eq(inventory.variantId, variant.variantId))
        .limit(1);

      if (existingInventory.length === 0) {
        variantsWithoutInventory.push(variant);
      }
    }

    // 4. Create missing inventory records
    const createdRecords = [];
    const errors = [];

    if (variantsWithoutInventory.length > 0) {
      for (const variant of variantsWithoutInventory) {
        try {
          const newInventory = await db.insert(inventory).values({
            productId: variant.productId,
            variantId: variant.variantId,
            quantity: 0 // Start with 0, admin can update later
          }).returning();

          createdRecords.push({
            color: variant.colorName,
            size: variant.sizeName,
            variantId: variant.variantId,
            inventoryId: newInventory[0].id
          });

          console.log(`✅ Created inventory for ${variant.colorName} ${variant.sizeName}`);
        } catch (error) {
          const errorMsg = `Failed to create inventory for ${variant.colorName} ${variant.sizeName}`;
          console.error(`❌ ${errorMsg}:`, error);
          errors.push(errorMsg);
        }
      }
    }

    // 5. Get summary of all inventory after fix
    const allInventoryAfterFix = await db
      .select({
        inventoryId: inventory.id,
        variantId: inventory.variantId,
        quantity: inventory.quantity,
        colorName: colors.name,
        sizeName: sizes.name
      })
      .from(inventory)
      .leftJoin(productVariants, eq(inventory.variantId, productVariants.id))
      .leftJoin(colors, eq(productVariants.colorId, colors.id))
      .leftJoin(sizes, eq(productVariants.sizeId, sizes.id))
      .where(eq(inventory.productId, product[0].id));

    const properInventory = allInventoryAfterFix.filter(record => record.colorName && record.sizeName);
    const legacyInventory = allInventoryAfterFix.filter(record => !record.sizeName);

    const result = {
      success: true,
      message: 'Inventory fix completed',
      summary: {
        totalProperVariants: properVariants.length,
        variantsWithoutInventory: variantsWithoutInventory.length,
        recordsCreated: createdRecords.length,
        errors: errors.length
      },
      createdRecords: createdRecords,
      errors: errors,
      currentInventory: {
        proper: properInventory.map(record => ({
          color: record.colorName,
          size: record.sizeName,
          quantity: record.quantity,
          inventoryId: record.inventoryId
        })),
        legacy: legacyInventory.map(record => ({
          color: record.colorName,
          quantity: record.quantity,
          inventoryId: record.inventoryId
        }))
      },
      timestamp: new Date().toISOString()
    };

    console.log('🎉 Inventory fix completed successfully');
    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ Inventory fix failed:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Unauthorized') {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
      if (error.message === 'Forbidden: Admin access required') {
        return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
      }
    }
    
    return NextResponse.json({
      error: 'Inventory fix failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
