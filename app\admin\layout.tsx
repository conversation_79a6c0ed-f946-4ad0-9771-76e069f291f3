"use client";

import { ReactNode, useState } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { SiteHeader } from "@/components/site-header";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/context/auth-context";

// Icons
import { 
  ShoppingCart, 
  PackageCheck, 
  Users, 
  LogOut 
} from "lucide-react";

interface AdminLayoutProps {
  children: ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const pathname = usePathname();
  const router = useRouter();
  const [collapsed, setCollapsed] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const { signOut } = useAuth();
  
  // Navigation items
  const navItems = [
    {
      title: "Orders",
      href: "/admin/orders",
      icon: <ShoppingCart className="h-5 w-5" />,
    },
    {
      title: "Inventory",
      href: "/admin/inventory",
      icon: <PackageCheck className="h-5 w-5" />,
    },
    {
      title: "Users",
      href: "/admin/users",
      icon: <Users className="h-5 w-5" />,
    },
  ];

  const handleAdminLogout = async () => {
    if (isLoggingOut) return;

    try {
      setIsLoggingOut(true);
      await signOut();
      router.push('/');
      router.refresh();
    } catch (error) {
      router.push('/');
      router.refresh();
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <>
      <SiteHeader />
      <div className="flex min-h-screen bg-neutral-950">
        {/* Sidebar */}
        <aside 
          className={`bg-sidebar border-r border-sidebar-border transition-all duration-300 ${
            collapsed ? "w-16" : "w-64"
          }`}
        >
          <div className="flex flex-col h-full">
            <div className="p-4 border-b border-sidebar-border flex items-center justify-between">
              {!collapsed && (
                <h2 className="text-lg font-semibold text-sidebar-foreground">Admin Panel</h2>
              )}
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setCollapsed(!collapsed)}
                className="text-sidebar-foreground hover:bg-sidebar-accent"
              >
                {collapsed ? "→" : "←"}
              </Button>
            </div>
            
            <nav className="flex-1 p-2">
              <ul className="space-y-1">
                {navItems.map((item) => {
                  const isActive = pathname === item.href;
                  return (
                    <li key={item.href}>
                      <Link 
                        href={item.href}
                        className={`flex items-center px-3 py-2 rounded-md transition-colors ${
                          isActive 
                            ? "bg-sidebar-primary text-sidebar-primary-foreground" 
                            : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                        }`}
                      >
                        <span className="mr-3">{item.icon}</span>
                        {!collapsed && <span>{item.title}</span>}
                      </Link>
                    </li>
                  );
                })}
              </ul>
            </nav>
            
            <div className="p-4 border-t border-sidebar-border mt-auto">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleAdminLogout}
                disabled={isLoggingOut}
                className={`w-full flex items-center justify-${collapsed ? "center" : "start"} text-sidebar-foreground hover:bg-sidebar-accent disabled:opacity-50`}
              >
                <LogOut className="h-5 w-5" />
                {!collapsed && <span className="ml-2">{isLoggingOut ? 'Logging out...' : 'Logout'}</span>}
              </Button>
            </div>
          </div>
        </aside>
        
        {/* Main content */}
        <main className="flex-1">
          {/* Top header */}
          <div className="border-b border-neutral-800 bg-card">
            <div className="flex items-center justify-between p-4">
              <h1 className="text-xl font-semibold text-foreground">
                {navItems.find(item => item.href === pathname)?.title || "Orders"}
              </h1>
              <div className="flex items-center space-x-4">
                <div className="text-sm text-muted-foreground">
                  <span>Admin User</span>
                </div>
                <div className="h-8 w-8 rounded-full bg-neutral-800 flex items-center justify-center text-primary">
                  A
                </div>
              </div>
            </div>
          </div>
          
          {/* Page content */}
          <div className="p-6">
            {children}
          </div>
        </main>
      </div>
    </>
  );
}
