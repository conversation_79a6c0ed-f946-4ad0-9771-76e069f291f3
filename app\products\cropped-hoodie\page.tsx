"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import Image from "next/image";
import { SiteHeader } from "@/components/site-header";
import { SiteFooter } from "@/components/site-footer";
import { Button } from "@/components/ui/button";
import { useCart } from "@/context/cart-context";

export default function CroppedHoodiePage() {
  const [selectedColor, setSelectedColor] = useState("Rustic Black");
  const [selectedSize, setSelectedSize] = useState("Medium");
  const [quantity, setQuantity] = useState(1);
  const [activeImage, setActiveImage] = useState(0);
  const [images, setImages] = useState<string[]>([]);
  const [showMagnifier, setShowMagnifier] = useState(false);
  const [magnifierPosition, setMagnifierPosition] = useState({ x: 0, y: 0 });
  const [cursorPosition, setCursorPosition] = useState({ x: 0, y: 0 });
  const [addedToCart, setAddedToCart] = useState(false);
  const [inventory, setInventory] = useState<Record<string, Record<string, number>>>({});
  const [loadingInventory, setLoadingInventory] = useState(true);
  
  const { addItem } = useCart();
  const imageContainerRef = useRef<HTMLDivElement>(null);

  const colors = [
    { name: "Rustic Black", value: "black" },
    { name: "Sky Blue", value: "sky-blue" },
    { name: "Matcha Green", value: "matcha-green" },
  ];

  const sizes = ["Small", "Medium", "Large", "XL"];

  // Fetch inventory data
  useEffect(() => {
    const fetchInventory = async () => {
      try {
        const response = await fetch('/api/products/inventory?productName=Cropped Zip Up Hoodie');
        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch inventory');
        }
        
        setInventory(data.inventory || {});
      } catch (error) {
        console.error('Error fetching inventory:', error);
        // Set default inventory values as fallback
        setInventory({
          'Rustic Black': { 'Small': 10, 'Medium': 15, 'Large': 20, 'XL': 5 },
          'Sky Blue': { 'Small': 8, 'Medium': 12, 'Large': 10, 'XL': 0 },
          'Matcha Green': { 'Small': 0, 'Medium': 0, 'Large': 0, 'XL': 0 }
        });
      } finally {
        setLoadingInventory(false);
      }
    };

    fetchInventory();
  }, []);

  // Reset the "Added to cart" message after 3 seconds
  useEffect(() => {
    let timeout: NodeJS.Timeout;
    if (addedToCart) {
      timeout = setTimeout(() => {
        setAddedToCart(false);
      }, 3000);
    }
    return () => clearTimeout(timeout);
  }, [addedToCart]);

  // Set the images based on the selected color
  useEffect(() => {
    let imageSet: string[] = [];
    
    switch(selectedColor) {
      case "Rustic Black":
        imageSet = [
          "/assets/black-hood.JPG",
          "/assets/black-back.JPG", 
          "/assets/black-no-hood.JPG",
          "/assets/all-top.JPG",
          "/assets/all-prints.JPG",
          "/assets/all-bot-left.JPG",
          "/assets/all-bot-right.JPG"
        ];
        break;
      case "Sky Blue":
        imageSet = [
          "/assets/blue-hood.JPG",
          "/assets/blue-back.JPG", 
          "/assets/blue-no-hood.JPG",
          "/assets/all-top.JPG",
          "/assets/all-prints.JPG",
          "/assets/all-bot-left.JPG",
          "/assets/all-bot-right.JPG"
        ];
        break;
      case "Matcha Green":
        imageSet = [
          "/assets/green-hood.JPG",
          "/assets/green-back.JPG", 
          "/assets/green-no-hood.JPG",
          "/assets/all-top.JPG",
          "/assets/all-prints.JPG",
          "/assets/all-bot-left.JPG",
          "/assets/all-bot-right.JPG"
        ];
        break;
      default:
        imageSet = ["/assets/all-top.JPG"];
    }
    
    setImages(imageSet);
    setActiveImage(0); // Reset to first image when color changes
  }, [selectedColor]);

  const handleColorChange = (color: string) => {
    // Only allow selection if the color has at least one size in stock
    const colorInventory = inventory[color];
    if (colorInventory && Object.values(colorInventory).some(qty => qty > 0)) {
      setSelectedColor(color);
    }
  };

  // Helper function to check if a color has any sizes in stock
  const isColorInStock = (colorName: string) => {
    const colorInventory = inventory[colorName];
    return colorInventory && Object.values(colorInventory).some(qty => qty > 0);
  };

  // Helper function to check if a specific size is in stock for the selected color
  const isSizeInStock = (size: string) => {
    return inventory[selectedColor]?.[size] > 0;
  };

  // Check if selected color is in stock when inventory loads
  useEffect(() => {
    if (!loadingInventory && !isColorInStock(selectedColor)) {
      // If current selected color is out of stock, switch to first available color
      const availableColor = colors.find(c => isColorInStock(c.name));
      if (availableColor) {
        setSelectedColor(availableColor.name);
      }
    }
  }, [inventory, loadingInventory]);

  const handleAddToCart = () => {
    addItem({
      id: "cropped-hoodie",
      name: "Cropped Zip Up Hoodie",
      price: "$0.05",
      color: selectedColor,
      size: selectedSize,
      quantity: quantity,
      imageSrc: images[0] || "/assets/all-top.JPG",
      imageAlt: `Cropped Zip Up Hoodie in ${selectedColor}`
    });
    
    setAddedToCart(true);
  };

  const handleImageMouseEnter = () => {
    setShowMagnifier(true);
  };

  const handleImageMouseLeave = () => {
    setShowMagnifier(false);
  };

  const handleImageMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (imageContainerRef.current) {
      const { left, top, width, height } = imageContainerRef.current.getBoundingClientRect();
      
      // Calculate cursor position relative to the image container
      const x = ((e.clientX - left) / width);
      const y = ((e.clientY - top) / height);
      
      // Limit values to stay within the image bounds
      const boundedX = Math.max(0, Math.min(1, x));
      const boundedY = Math.max(0, Math.min(1, y));

      setCursorPosition({ x: boundedX * 100, y: boundedY * 100 });
      
      // Calculate magnifier position to be directly above cursor
      const magnifierSize = 200; // Width and height of the magnifier
      const magnifierOffset = 30; // Offset above cursor
      let magX = e.clientX - magnifierSize / 2;
      let magY = e.clientY - magnifierSize - magnifierOffset; // Position above cursor
      
      // Keep magnifier within viewport
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      
      magX = Math.max(0, Math.min(viewportWidth - magnifierSize, magX));
      magY = Math.max(0, Math.min(viewportHeight - magnifierSize, magY));
      
      setMagnifierPosition({ x: magX, y: magY });
    }
  };

  const handleImageTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
    setShowMagnifier(true);
    handleImageTouchMove(e);
  };

  const handleImageTouchMove = (e: React.TouchEvent<HTMLDivElement>) => {
    if (imageContainerRef.current && e.touches[0]) {
      const { left, top, width, height } = imageContainerRef.current.getBoundingClientRect();
      
      // Calculate cursor position relative to the image container
      const x = ((e.touches[0].clientX - left) / width);
      const y = ((e.touches[0].clientY - top) / height);
      
      // Limit values to stay within the image bounds
      const boundedX = Math.max(0, Math.min(1, x));
      const boundedY = Math.max(0, Math.min(1, y));

      setCursorPosition({ x: boundedX * 100, y: boundedY * 100 });
      
      // Calculate magnifier position to be directly above touch point
      const magnifierSize = 200; // Width and height of the magnifier
      const magnifierOffset = 30; // Offset above cursor
      let magX = e.touches[0].clientX - magnifierSize / 2;
      let magY = e.touches[0].clientY - magnifierSize - magnifierOffset; // Position above cursor
      
      // Keep magnifier within viewport
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      
      magX = Math.max(0, Math.min(viewportWidth - magnifierSize, magX));
      magY = Math.max(0, Math.min(viewportHeight - magnifierSize, magY));
      
      setMagnifierPosition({ x: magX, y: magY });
    }
  };

  const handleImageTouchEnd = () => {
    setShowMagnifier(false);
  };

  return (
    <div className="min-h-screen flex flex-col bg-neutral-900 text-white">
      <SiteHeader />

      <main className="flex-grow pt-4 pb-16">
        <div className="container mx-auto px-4 md:px-6">
          {/* Breadcrumbs */}
          <div className="mb-6 flex items-center text-sm text-gray-400">
            <Link href="/" className="hover:text-white transition-colors">Home</Link>
            <span className="mx-2">/</span>
            <Link href="/#shop" className="hover:text-white transition-colors">Shop</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-200">Cropped Zip Up Hoodie</span>
          </div>
          
          {/* Product Display */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-10 lg:gap-16">
            {/* Product Images */}
            <div className="space-y-4">
              <div className="max-w-[600px] mx-auto bg-neutral-800 relative rounded-md border border-neutral-700 shadow-sm">
                {images.length > 0 && (
                  <div 
                    ref={imageContainerRef}
                    className="aspect-[4/3] relative flex items-center justify-center cursor-crosshair overflow-hidden"
                    onMouseEnter={handleImageMouseEnter}
                    onMouseLeave={handleImageMouseLeave}
                    onMouseMove={handleImageMouseMove}
                    onTouchStart={handleImageTouchStart}
                    onTouchMove={handleImageTouchMove}
                    onTouchEnd={handleImageTouchEnd}
                  >
                    <Image
                      src={images[activeImage]}
                      alt={`Cropped Zip Up Hoodie in ${selectedColor}`}
                      width={800}
                      height={800}
                      className={`w-full h-full object-contain ${
                        (images[activeImage].includes('black-back.JPG') || 
                         images[activeImage].includes('blue-back.JPG')) ? 
                        'rotate-[-90deg] scale-[1.35] origin-center' : ''
                      }`}
                      sizes="(max-width: 768px) 100vw, 50vw"
                      priority={activeImage === 0}
                    />
                  </div>
                )}
                
                {/* Magnifier Lens */}
                {showMagnifier && (
                  <div 
                    className="absolute pointer-events-none rounded-lg shadow-lg border-2 border-neutral-700 overflow-hidden bg-neutral-800 z-50"
                    style={{
                      left: `${magnifierPosition.x}px`,
                      top: `${magnifierPosition.y}px`,
                      width: '200px',
                      height: '200px'
                    }}
                  >
                    <div
                      style={{
                        width: '100%',
                        height: '100%',
                        backgroundImage: `url(${images[activeImage]})`,
                        backgroundPosition: `${cursorPosition.x}% ${cursorPosition.y}%`,
                        backgroundRepeat: 'no-repeat',
                        backgroundSize: '500%',
                        transform: (images[activeImage].includes('black-back.JPG') || 
                                    images[activeImage].includes('blue-back.JPG')) ? 
                                    'rotate(-90deg) scale(1.35)' : 'none',
                        transformOrigin: 'center'
                      }}
                    />
                  </div>
                )}
              </div>
              
              {/* Carousel for thumbnails */}
              <div className="relative">
                <div className="overflow-hidden">
                  <div 
                    className="flex transition-transform duration-300 ease-in-out space-x-2"
                    style={{
                      transform: `translateX(-${Math.floor(activeImage / 4) * 100}%)`
                    }}
                  >
                    {images.map((img, index) => (
                      <button 
                        key={index} 
                        className={`flex-none w-1/4 aspect-square bg-neutral-800 flex items-center justify-center text-xs text-gray-400 cursor-pointer rounded border relative overflow-hidden ${
                          activeImage === index ? 'border-white ring-1 ring-white' : 'border-neutral-700 hover:border-neutral-500'
                        }`}
                        onClick={() => setActiveImage(index)}
                      >
                        <Image
                          src={img}
                          alt={`Thumbnail ${index + 1}`}
                          width={100}
                          height={100}
                          className={`object-contain w-full h-full ${
                            (img.includes('black-back.JPG') || 
                             img.includes('blue-back.JPG')) ? 
                            'rotate-[-90deg] scale-[0.85] origin-center' : ''
                          }`}
                          sizes="(max-width: 768px) 25vw, 10vw"
                        />
                      </button>
                    ))}
                  </div>
                </div>
                
                {/* Carousel Navigation Buttons */}
                {images.length > 4 && (
                  <>
                    <button 
                      onClick={() => setActiveImage(Math.max(0, activeImage - 4))}
                      className={`absolute left-0 top-1/2 -translate-y-1/2 bg-neutral-800/80 backdrop-blur-sm p-2 rounded-full shadow-sm z-10 ${
                        activeImage === 0 ? 'opacity-50 cursor-not-allowed' : ''
                      }`}
                      disabled={activeImage === 0}
                    >
                      <svg 
                        xmlns="http://www.w3.org/2000/svg" 
                        width="20" 
                        height="20" 
                        viewBox="0 0 24 24" 
                        fill="none" 
                        stroke="currentColor" 
                        strokeWidth="2" 
                        strokeLinecap="round" 
                        strokeLinejoin="round"
                        className="text-white"
                      >
                        <polyline points="15 18 9 12 15 6"></polyline>
                      </svg>
                    </button>
                    <button 
                      onClick={() => setActiveImage(Math.min(images.length - 1, activeImage + 4))}
                      className={`absolute right-0 top-1/2 -translate-y-1/2 bg-neutral-800/80 backdrop-blur-sm p-2 rounded-full shadow-sm z-10 ${
                        Math.floor(activeImage / 4) >= Math.ceil(images.length / 4) - 1 ? 'opacity-50 cursor-not-allowed' : ''
                      }`}
                      disabled={Math.floor(activeImage / 4) >= Math.ceil(images.length / 4) - 1}
                    >
                      <svg 
                        xmlns="http://www.w3.org/2000/svg" 
                        width="20" 
                        height="20" 
                        viewBox="0 0 24 24" 
                        fill="none" 
                        stroke="currentColor" 
                        strokeWidth="2" 
                        strokeLinecap="round" 
                        strokeLinejoin="round"
                        className="text-white"
                      >
                        <polyline points="9 18 15 12 9 6"></polyline>
                      </svg>
                    </button>
                  </>
                )}
              </div>
            </div>

            {/* Product Details */}
            <div className="flex flex-col">
              <div className="mb-1 inline-flex">
                <span className="text-xs font-medium uppercase tracking-wider bg-gray-700 text-white px-2 py-1">New</span>
              </div>
              <h1 className="text-3xl font-medium text-white">Cropped Zip Up Hoodie</h1>
              <p className="mt-2 text-2xl font-light text-gray-300">$0.50</p>


              <div className="mt-6 text-sm text-gray-300 leading-relaxed">
                <p>Classic cropped hoodie with zip-up front. Made from premium technical nylon with an elasticated waist band for comfort and style.</p>
              </div>

              {/* Color Selection */}
              <div className="mt-8">
                <p className="text-sm font-medium mb-3 text-white">
                  COLOR: <span className="ml-2 text-gray-400">{selectedColor}</span>
                  {!loadingInventory && !isColorInStock(selectedColor) && (
                    <span className="ml-2 text-red-500 text-xs">(Out of Stock)</span>
                  )}
                </p>
                <div className="flex gap-3">
                  {colors.map((color) => {
                    const isOutOfStock = !loadingInventory && !isColorInStock(color.name);
                    return (
                      <button
                        key={color.name}
                        className={`w-9 h-9 rounded-full flex items-center justify-center relative ${
                          selectedColor === color.name ? 'ring-2 ring-offset-2 ring-white' : ''
                        } ${isOutOfStock ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:ring-1 hover:ring-offset-1 hover:ring-gray-400'}`}
                        onClick={() => handleColorChange(color.name)}
                        aria-label={`Select ${color.name} color${isOutOfStock ? ' (Out of Stock)' : ''}`}
                        disabled={isOutOfStock}
                        title={isOutOfStock ? 'Out of Stock' : color.name}
                      >
                        <span 
                          className="w-7 h-7 rounded-full relative" 
                          style={{ 
                            backgroundColor: 
                              color.value === 'black' ? '#121212' : 
                              color.value === 'sky-blue' ? '#87CEEB' : 
                              color.value === 'matcha-green' ? '#8DC73F' : '' 
                          }}
                        >
                          {isOutOfStock && (
                            <span className="absolute inset-0 flex items-center justify-center">
                              <svg className="w-6 h-6 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </span>
                          )}
                        </span>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Size Selection */}
              <div className="mt-8">
                <div className="flex justify-between items-center mb-3">
                  <p className="text-sm font-medium text-white">SIZE: <span className="ml-2 text-gray-400">{selectedSize}</span></p>
                  <button className="text-sm text-gray-400 underline hover:text-white">Size Guide</button>
                </div>
                <div className="grid grid-cols-4 gap-2">
                  {sizes.map((size) => {
                    const isOutOfStock = !loadingInventory && !isSizeInStock(size);
                    return (
                      <button
                        key={size}
                        className={`py-3 text-sm border transition-all ${
                          selectedSize === size 
                            ? 'bg-white text-neutral-900 border-white' 
                            : isOutOfStock
                              ? 'text-gray-500 border-neutral-800 cursor-not-allowed opacity-50'
                              : 'text-gray-300 border-neutral-700 hover:border-gray-500'
                        }`}
                        onClick={() => !isOutOfStock && setSelectedSize(size)}
                        disabled={isOutOfStock}
                        title={isOutOfStock ? 'Out of Stock' : size}
                      >
                        {size}
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Quantity */}
              <div className="mt-8">
                <p className="text-sm font-medium mb-3 text-white">QUANTITY:</p>
                <div className="flex border border-neutral-700 w-32 h-12 rounded-sm text-white">
                  <button 
                    className="w-10 h-full flex items-center justify-center border-r border-neutral-700 hover:bg-neutral-800"
                    onClick={() => setQuantity(prev => Math.max(1, prev - 1))}
                  >
                    <svg 
                      xmlns="http://www.w3.org/2000/svg" 
                      width="16" 
                      height="16" 
                      viewBox="0 0 24 24" 
                      fill="none" 
                      stroke="currentColor" 
                      strokeWidth="2" 
                      strokeLinecap="round" 
                      strokeLinejoin="round"
                    >
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                  </button>
                  <div className="flex-1 flex items-center justify-center font-medium">{quantity}</div>
                  <button 
                    className="w-10 h-full flex items-center justify-center border-l border-neutral-700 hover:bg-neutral-800"
                    onClick={() => setQuantity(prev => prev + 1)}
                  >
                    <svg 
                      xmlns="http://www.w3.org/2000/svg" 
                      width="16" 
                      height="16" 
                      viewBox="0 0 24 24" 
                      fill="none" 
                      stroke="currentColor" 
                      strokeWidth="2" 
                      strokeLinecap="round" 
                      strokeLinejoin="round"
                    >
                      <line x1="12" y1="5" x2="12" y2="19"></line>
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                  </button>
                </div>
              </div>

              {/* Actions */}
              <div className="mt-10 grid gap-4">
                <Button 
                  className={`rounded-none py-6 transition-all duration-200 flex items-center justify-center gap-2 ${
                    addedToCart 
                      ? 'bg-green-600 hover:bg-green-700 text-white' 
                      : (!loadingInventory && !isSizeInStock(selectedSize))
                        ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        : 'bg-white hover:bg-gray-200 text-neutral-900'
                  }`}
                  onClick={handleAddToCart}
                  disabled={!loadingInventory && !isSizeInStock(selectedSize)}
                >
                  {addedToCart ? (
                    <>
                      <svg 
                        xmlns="http://www.w3.org/2000/svg" 
                        width="18" 
                        height="18" 
                        viewBox="0 0 24 24" 
                        fill="none" 
                        stroke="currentColor" 
                        strokeWidth="2" 
                        strokeLinecap="round" 
                        strokeLinejoin="round"
                      >
                        <path d="M20 6L9 17l-5-5" />
                      </svg>
                      ADDED TO CART
                    </>
                  ) : (!loadingInventory && !isSizeInStock(selectedSize)) ? (
                    'OUT OF STOCK'
                  ) : (
                    <>
                      <svg 
                        xmlns="http://www.w3.org/2000/svg" 
                        width="18" 
                        height="18" 
                        viewBox="0 0 24 24" 
                        fill="none" 
                        stroke="currentColor" 
                        strokeWidth="2" 
                        strokeLinecap="round" 
                        strokeLinejoin="round"
                      >
                        <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path>
                        <line x1="3" y1="6" x2="21" y2="6"></line>
                        <path d="M16 10a4 4 0 0 1-8 0"></path>
                      </svg>
                      ADD TO CART
                    </>
                  )}
                </Button>
                <Button 
                  variant="outline" 
                  className={`border-white rounded-none py-6 transition-all duration-200 ${
                    (!loadingInventory && !isSizeInStock(selectedSize))
                      ? 'text-gray-400 border-gray-600 cursor-not-allowed'
                      : 'text-white hover:bg-neutral-800'
                  }`}
                  disabled={!loadingInventory && !isSizeInStock(selectedSize)}
                >
                  BUY NOW
                </Button>
              </div>

              {/* Additional Info */}
              <div className="mt-10 space-y-6">
                <div className="border-t border-neutral-700 pt-6">
                  <details className="group">
                    <summary className="flex cursor-pointer items-center justify-between text-white">
                      <h3 className="text-sm font-medium">Product Details</h3>
                      <span className="ml-2 shrink-0 transition duration-300 group-open:-rotate-180">
                        <svg 
                          xmlns="http://www.w3.org/2000/svg" 
                          width="16" 
                          height="16" 
                          viewBox="0 0 24 24" 
                          fill="none" 
                          stroke="currentColor" 
                          strokeWidth="2" 
                          strokeLinecap="round" 
                          strokeLinejoin="round"
                        >
                          <polyline points="6 9 12 15 18 9"></polyline>
                        </svg>
                      </span>
                    </summary>
                    <div className="mt-4 text-sm text-gray-300 leading-relaxed">
                      <ul className="list-disc pl-5 space-y-1">
                        <li>Technical nylon fabric</li>
                        <li>Elasticated waist band</li>
                        <li>Side pockets</li>
                        <li>Zip front closure</li>
                        <li>Cropped length</li>
                      </ul>
                    </div>
                  </details>
                </div>
                
                <div className="border-t border-neutral-700 pt-6">
                  <details className="group">
                    <summary className="flex cursor-pointer items-center justify-between text-white">
                      <h3 className="text-sm font-medium">Shipping & Returns</h3>
                      <span className="ml-2 shrink-0 transition duration-300 group-open:-rotate-180">
                        <svg 
                          xmlns="http://www.w3.org/2000/svg" 
                          width="16" 
                          height="16" 
                          viewBox="0 0 24 24" 
                          fill="none" 
                          stroke="currentColor" 
                          strokeWidth="2" 
                          strokeLinecap="round" 
                          strokeLinejoin="round"
                        >
                          <polyline points="6 9 12 15 18 9"></polyline>
                        </svg>
                      </span>
                    </summary>
                    <div className="mt-4 text-sm text-gray-300 leading-relaxed">
                      <p>Free shipping on all orders over $100. Free returns within 30 days of delivery.</p>
                    </div>
                  </details>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <SiteFooter />
    </div>
  );
} 