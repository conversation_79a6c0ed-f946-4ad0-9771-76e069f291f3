import { db } from './index';
import { inventory, products, productVariants, colors, sizes } from './schema';
import { eq, desc } from 'drizzle-orm';

export async function getProductInventory(productName: string) {
  try {
    const product = await db
      .select()
      .from(products)
      .where(eq(products.name, productName))
      .limit(1);

    if (!product || product.length === 0) {
      throw new Error('Product not found');
    }

    const inventoryData = await db
      .select({
        colorName: colors.name,
        colorValue: colors.value,
        sizeName: sizes.name,
        sizeValue: sizes.value,
        quantity: inventory.quantity,
      })
      .from(inventory)
      .leftJoin(productVariants, eq(inventory.variantId, productVariants.id))
      .leftJoin(colors, eq(productVariants.colorId, colors.id))
      .leftJoin(sizes, eq(productVariants.sizeId, sizes.id))
      .where(eq(inventory.productId, product[0].id));

    // Return inventory grouped by color and size
    const inventoryByColorSize: Record<string, Record<string, number>> = {};
    
    inventoryData.forEach(item => {
      if (item.colorName && item.sizeName) {
        if (!inventoryByColorSize[item.colorName]) {
          inventoryByColorSize[item.colorName] = {};
        }
        inventoryByColorSize[item.colorName][item.sizeName] = item.quantity || 0;
      }
    });

    return inventoryByColorSize;
  } catch (error) {
    console.error('Error fetching product inventory:', error);
    throw error;
  }
}

export async function getAllInventory() {
  try {
    const inventoryData = await db
      .select({
        id: inventory.id,
        productId: products.id,
        productName: products.name,
        sku: products.sku,
        variantId: productVariants.id,
        variantSku: productVariants.sku,
        colorName: colors.name,
        colorValue: colors.value,
        sizeName: sizes.name,
        sizeValue: sizes.value,
        quantity: inventory.quantity,
        updatedAt: inventory.updatedAt,
      })
      .from(inventory)
      .leftJoin(products, eq(inventory.productId, products.id))
      .leftJoin(productVariants, eq(inventory.variantId, productVariants.id))
      .leftJoin(colors, eq(productVariants.colorId, colors.id))
      .leftJoin(sizes, eq(productVariants.sizeId, sizes.id))
      .orderBy(desc(inventory.updatedAt));

    return inventoryData;
  } catch (error) {
    console.error('Error fetching all inventory:', error);
    throw error;
  }
}

export async function updateInventoryQuantity(inventoryId: string, quantity: number) {
  try {
    await db
      .update(inventory)
      .set({ 
        quantity: quantity,
        updatedAt: new Date().toISOString()
      })
      .where(eq(inventory.id, inventoryId));

    return { success: true };
  } catch (error) {
    console.error('Error updating inventory:', error);
    throw error;
  }
}