// Type definitions for Cypress global objects
declare namespace Cypress {
  interface Chainable {
    // Add custom Cypress commands here if needed
  }
}

// Declare global Cypress test functions
declare const describe: (description: string, callback: () => void) => void;
declare const beforeEach: (callback: () => void) => void;
declare const it: {
  (description: string, callback: () => void): void;
  skip(description: string, callback: () => void): void;
};
declare const cy: Cypress.Chainable;