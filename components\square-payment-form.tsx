'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';

// Define a basic type for the Square SDK object on window
interface SquareSDK {
  payments(applicationId: string, locationId: string): SquarePayments;
}

// Define a basic type for the Payments object
interface SquarePayments {
  card(): Promise<SquareCard>;
}

// Define a basic type for the Card object
interface SquareCard {
  attach(selector: string): Promise<void>;
  tokenize(): Promise<TokenizeResult>;
  destroy?(): Promise<void>; // Add optional destroy method
  focus?(field: string): Promise<boolean>; // Add focus method signature
}

// Define a basic type for the Tokenize result
interface TokenizeResult {
  status: string;
  token?: string;
  errors?: Array<{ message: string; field?: string }>; // Add optional field to error type
}

declare global {
  interface Window {
    Square?: SquareSDK; // Use the defined type
  }
}

interface SquarePaymentFormProps {
  applicationId: string;
  locationId: string;
  amount: number;
  orderData?: unknown;
  onPaymentSuccess: (paymentDetails: Record<string, unknown>) => void; // Use Record
  onPaymentFailure: (error: unknown) => void; // Use unknown
}

const SquarePaymentForm: React.FC<SquarePaymentFormProps> = ({ 
  applicationId, 
  locationId, 
  amount, 
  orderData,
  onPaymentSuccess, 
  onPaymentFailure 
}) => {

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const cardInstanceRef = useRef<SquareCard | null>(null);
  const cardContainerRef = useRef<HTMLDivElement>(null);
  const cardButtonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (!applicationId || !locationId || !cardContainerRef.current) {
      setError('Payment form cannot load.');
      return;
    }
    cardContainerRef.current.innerHTML = '';
    if (!window.Square) {
      setError('Failed to load payment SDK.');
      return;
    }

    let isMounted = true; 

    async function initializeSquare() {
      try {
        const payments = window.Square!.payments(applicationId, locationId);
        const card = await payments.card();
        
        if (isMounted && cardContainerRef.current) {
            await card.attach('#card-container'); 
            cardInstanceRef.current = card;
        }
      } catch (e: unknown) {
        setError('Failed to initialize payment form.');
      }
    }

    initializeSquare();

    // Cleanup function
    return () => {
      isMounted = false;
      const cardToDestroy = cardInstanceRef.current;
      if (cardToDestroy) {
        if (typeof cardToDestroy.destroy === 'function') {
          cardToDestroy.destroy().catch((destroyError: unknown) => {
            console.error("Error destroying Square card:", destroyError);
          });
        }
        cardInstanceRef.current = null; 
      }
    };
  }, [applicationId, locationId]);

  const handlePayment = async () => {
    setIsLoading(true);
    setError(null);

    const card = cardInstanceRef.current;
    if (!card) {
      setError('Payment form not initialized or card instance missing.');
      setIsLoading(false);
      return;
    }

    try {
      const result = await card.tokenize(); 
      
      if (result.status !== 'OK') {
        let validationError = "Card input is invalid.";
        if (result.errors && result.errors.length > 0) {
            const firstErrorField = result.errors[0].field;
            if (firstErrorField && typeof card.focus === 'function') { 
                card.focus(firstErrorField).catch((e: unknown) => console.error("Error focusing field:", e));
            }
            validationError = result.errors.map(e => e.message).join(' \n');
        }
        setError(validationError);
        setIsLoading(false);
        return;
      }

      const token = result.token!;
      
      const response = await fetch('/api/create-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sourceId: token, amount: amount, orderData }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        onPaymentSuccess(data.payment);
      } else {
        throw new Error(data.details || data.error || 'Payment processing failed on server');
      }
    } catch (err: unknown) {
      let message = 'An unknown error occurred during payment.';
      if (err instanceof Error) {
        message = err.message;
      }
      setError(message);
      onPaymentFailure(err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div id="card-container" ref={cardContainerRef} className="border border-neutral-600 p-3 rounded bg-neutral-900"></div>

      {error && <p className="text-red-300 text-sm">Error: {error}</p>}

      <Button 
        onClick={handlePayment}
        type="button" 
        ref={cardButtonRef} 
        disabled={isLoading} 
        className="w-full rounded-none py-6 bg-white text-black hover:bg-gray-200 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading ? 'Processing...' : `Pay $${amount.toFixed(2)}`}
      </Button>
    </div>
  );
};

export default SquarePaymentForm; 