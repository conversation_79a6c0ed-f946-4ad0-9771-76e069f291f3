'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { createClient } from '@/utils/supabase/client'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/context/auth-context'

type AuthView = 'sign_in' | 'sign_up'

export function AuthCustomForm() {
  const [view, setView] = useState<AuthView>('sign_in')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [errorMsg, setErrorMsg] = useState<string | null>(null)
  const [successMsg, setSuccessMsg] = useState<string | null>(null)
  
  const supabase = createClient()
  const router = useRouter()
  const { refreshSession } = useAuth()
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setErrorMsg(null)
    setSuccessMsg(null)
    
    const supabase = createClient()
    
    try {
      if (view === 'sign_in') {
        const { error } = await supabase.auth.signInWithPassword({
          email,
          password
        })
        
        if (error) throw error

        // Refresh the auth context to sync state
        await refreshSession()

        router.push('/')
        router.refresh()
      } else {
        // Validate that passwords match
        if (password !== confirmPassword) {
          setErrorMsg('Passwords do not match')
          setLoading(false)
          return
        }
        
        const { error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            emailRedirectTo: `${window.location.origin}/auth/callback`
          }
        })
        
        if (error) throw error
        
        setSuccessMsg('Check your email for the confirmation link')
      }
    } catch (error: unknown) {
      const errorMsg = error instanceof Error ? error.message : 'An error occurred'
      setErrorMsg(errorMsg)
    } finally {
      setLoading(false)
    }
  }
  
  const toggleView = () => {
    setView(view === 'sign_in' ? 'sign_up' : 'sign_in')
    setErrorMsg(null)
    setSuccessMsg(null)
  }
  
  const handleGoogleSignIn = async () => {
    setLoading(true)
    setErrorMsg(null)
    
    const supabase = createClient()
    
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: process.env.NEXT_PUBLIC_SITE_URL 
            ? `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`
            : `${window.location.origin}/auth/callback`
        }
      })
      
      if (error) throw error
    } catch (error: unknown) {
      const errorMsg = error instanceof Error ? error.message : 'An error occurred'
      setErrorMsg(errorMsg)
      setLoading(false)
    }
  }
  
  return (
    <div className="w-full text-white">
      {errorMsg && (
        <div className="mb-4 p-3 bg-red-900/30 border border-red-800 text-red-200">
          {errorMsg}
        </div>
      )}
      
      {successMsg && (
        <div className="mb-4 p-3 bg-green-900/30 border border-green-800 text-green-200">
          {successMsg}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <Button
          type="button"
          onClick={handleGoogleSignIn}
          disabled={loading}
          className="w-full bg-white hover:bg-gray-200 text-black rounded-none font-medium py-3"
        >
          {view === 'sign_in' ? 'SIGN IN WITH GOOGLE' : 'SIGN UP WITH GOOGLE'}
        </Button>
        
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-neutral-800"></div>
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-neutral-900 px-2 text-neutral-400">Or continue with email</span>
          </div>
        </div>
        
        <div>
          <Label htmlFor="email" className="text-white">
            Email address
          </Label>
          <Input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            placeholder="Your email address"
            className="mt-1 bg-neutral-800 border-neutral-700 text-white rounded-none"
          />
        </div>
        
        <div>
          <Label htmlFor="password" className="text-white">
            {view === 'sign_up' ? 'Create password' : 'Password'}
          </Label>
          <Input
            id="password"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            placeholder="Your password"
            className="mt-1 bg-neutral-800 border-neutral-700 text-white rounded-none"
          />
        </div>
        
        {view === 'sign_up' && (
          <div>
            <Label htmlFor="confirmPassword" className="text-white">
              Retype Password
            </Label>
            <Input
              id="confirmPassword"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
              placeholder="Retype your password"
              className="mt-1 bg-neutral-800 border-neutral-700 text-white rounded-none"
            />
          </div>
        )}
        
        <Button
          type="submit"
          disabled={loading}
          className="w-full bg-white hover:bg-gray-200 text-black rounded-none font-medium py-3"
        >
          {loading ? 'PROCESSING...' : view === 'sign_in' ? 'SIGN IN WITH EMAIL' : 'CREATE ACCOUNT'}
        </Button>
        
        <div className="text-center text-sm text-gray-400">
          {view === 'sign_in' ? "Don't have an account? " : "Already have an account? "}
          <button
            type="button"
            onClick={toggleView}
            className="text-gray-400 hover:text-white underline"
          >
            {view === 'sign_in' ? 'Sign up' : 'Sign in'}
          </button>
        </div>
      </form>
    </div>
  )
}