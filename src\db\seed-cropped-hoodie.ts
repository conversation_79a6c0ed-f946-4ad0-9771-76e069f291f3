import { db } from './index';
import { products, colors, sizes, productVariants, inventory } from './schema';
import { eq, and } from 'drizzle-orm';

async function seedCroppedHoodie() {
  try {
    console.log('Creating cropped hoodie product with inventory...');
    
    // Check if product already exists
    const existingProduct = await db
      .select()
      .from(products)
      .where(eq(products.name, 'Cropped Zip Up Hoodie'))
      .limit(1);

    let productId: string;

    if (existingProduct.length > 0) {
      productId = existingProduct[0].id;
      console.log('Product already exists, using existing product ID:', productId);
    } else {
      // Insert the cropped hoodie product
      const [product] = await db.insert(products).values({
        userId: 'admin',
        name: 'Cropped Zip Up Hoodie',
        description: 'Classic cropped hoodie with zip-up front. Made from premium technical nylon with an elasticated waist band for comfort and style.',
        price: '0.50',
        featuredImageUrl: '/assets/all-top.JPG',
        status: 'active',
        sku: 'CROP-HOOD-001'
      }).returning();
      
      productId = product.id;
      console.log('Product created with ID:', productId);
    }

    // Check and insert colors if they don't exist
    const colorData = [
      { name: 'Rustic Black', value: '#121212' },
      { name: 'Sky Blue', value: '#87CEEB' },
      { name: 'Matcha Green', value: '#8DC73F' }
    ];

    const colorIds: { [key: string]: string } = {};

    for (const colorInfo of colorData) {
      const existingColor = await db
        .select()
        .from(colors)
        .where(eq(colors.name, colorInfo.name))
        .limit(1);

      if (existingColor.length > 0) {
        colorIds[colorInfo.name] = existingColor[0].id;
        console.log(`Color ${colorInfo.name} already exists`);
      } else {
        const [color] = await db.insert(colors).values({
          name: colorInfo.name,
          value: colorInfo.value
        }).returning();
        colorIds[colorInfo.name] = color.id;
        console.log(`Color ${colorInfo.name} created`);
      }
    }

    // Check and insert sizes if they don't exist
    const sizeData = [
      { name: 'Small', value: 'S' },
      { name: 'Medium', value: 'M' },
      { name: 'Large', value: 'L' },
      { name: 'XL', value: 'XL' }
    ];

    const sizeIds: { [key: string]: string } = {};

    for (const sizeInfo of sizeData) {
      const existingSize = await db
        .select()
        .from(sizes)
        .where(eq(sizes.name, sizeInfo.name))
        .limit(1);

      if (existingSize.length > 0) {
        sizeIds[sizeInfo.name] = existingSize[0].id;
        console.log(`Size ${sizeInfo.name} already exists`);
      } else {
        const [size] = await db.insert(sizes).values({
          name: sizeInfo.name,
          value: sizeInfo.value
        }).returning();
        sizeIds[sizeInfo.name] = size.id;
        console.log(`Size ${sizeInfo.name} created`);
      }
    }

    // Create product variants for each color+size combination
    const variantIds: { [key: string]: string } = {};

    for (const colorInfo of colorData) {
      for (const sizeInfo of sizeData) {
        const variantKey = `${colorInfo.name}-${sizeInfo.name}`;
        
        // Check if variant already exists
        const existingVariant = await db
          .select()
          .from(productVariants)
          .where(
            and(
              eq(productVariants.productId, productId),
              eq(productVariants.colorId, colorIds[colorInfo.name]),
              eq(productVariants.sizeId, sizeIds[sizeInfo.name])
            )
          )
          .limit(1);

        if (existingVariant.length > 0) {
          variantIds[variantKey] = existingVariant[0].id;
          console.log(`Variant for ${variantKey} already exists`);
        } else {
          const [variant] = await db.insert(productVariants).values({
            productId: productId,
            colorId: colorIds[colorInfo.name],
            sizeId: sizeIds[sizeInfo.name],
            price: '0.50',
            sku: `CROP-HOOD-${colorInfo.name.toUpperCase().replace(' ', '-')}-${sizeInfo.value}`
          }).returning();
          variantIds[variantKey] = variant.id;
          console.log(`Variant for ${variantKey} created`);
        }
      }
    }

    // Create inventory records for each variant
    // Different inventory levels for testing
    const inventoryData: { [key: string]: number } = {
      'Rustic Black-Small': 10,
      'Rustic Black-Medium': 15,
      'Rustic Black-Large': 20,
      'Rustic Black-XL': 5,
      'Sky Blue-Small': 8,
      'Sky Blue-Medium': 12,
      'Sky Blue-Large': 10,
      'Sky Blue-XL': 0, // Out of stock for testing
      'Matcha Green-Small': 0, // Out of stock for testing
      'Matcha Green-Medium': 0, // Out of stock for testing
      'Matcha Green-Large': 0, // Out of stock for testing
      'Matcha Green-XL': 0 // Out of stock for testing
    };

    for (const [variantKey, quantity] of Object.entries(inventoryData)) {
      const variantId = variantIds[variantKey];
      
      if (!variantId) {
        console.error(`Variant not found for ${variantKey}`);
        continue;
      }
      
      // Check if inventory record already exists
      const existingInventory = await db
        .select()
        .from(inventory)
        .where(eq(inventory.variantId, variantId))
        .limit(1);

      if (existingInventory.length > 0) {
        // Update existing inventory
        await db
          .update(inventory)
          .set({ 
            quantity: quantity,
            updatedAt: new Date().toISOString()
          })
          .where(eq(inventory.id, existingInventory[0].id));
        console.log(`Updated inventory for ${variantKey}: ${quantity} units`);
      } else {
        // Create new inventory record
        await db.insert(inventory).values({
          productId: productId,
          variantId: variantId,
          quantity: quantity
        });
        console.log(`Created inventory for ${variantKey}: ${quantity} units`);
      }
    }
    
    console.log('Cropped hoodie product and inventory created successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error creating cropped hoodie:', error);
    process.exit(1);
  }
}

seedCroppedHoodie();