"use client";

import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Search, Filter, UserPlus, Mail, MoreHorizontal } from "lucide-react";

export default function UsersManagement() {
  // Placeholder data for users
  const users = [
    { id: "USR-001", name: "<PERSON>", email: "<EMAIL>", role: "Customer", status: "Active", lastLogin: "2025-05-18" },
    { id: "USR-002", name: "<PERSON>", email: "<EMAIL>", role: "Customer", status: "Active", lastLogin: "2025-05-17" },
    { id: "USR-003", name: "<PERSON>", email: "<EMAIL>", role: "Admin", status: "Active", lastLogin: "2025-05-19" },
    { id: "USR-004", name: "<PERSON>", email: "<EMAIL>", role: "Customer", status: "Inactive", lastLogin: "2025-05-10" },
    { id: "USR-005", name: "<PERSON>", email: "<EMAIL>", role: "Customer", status: "Active", lastLogin: "2025-05-16" },
    { id: "USR-006", name: "<PERSON>", email: "<EMAIL>", role: "Staff", status: "Active", lastLogin: "2025-05-18" },
    { id: "USR-007", name: "David Taylor", email: "<EMAIL>", role: "Customer", status: "Active", lastLogin: "2025-05-15" },
    { id: "USR-008", name: "Lisa Anderson", email: "<EMAIL>", role: "Customer", status: "Suspended", lastLogin: "2025-05-05" },
    { id: "USR-009", name: "James Martin", email: "<EMAIL>", role: "Staff", status: "Active", lastLogin: "2025-05-18" },
    { id: "USR-010", name: "Jennifer White", email: "<EMAIL>", role: "Customer", status: "Active", lastLogin: "2025-05-14" },
  ];

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-semibold">Users Management</h1>
        <Button size="sm" className="flex items-center gap-1">
          <UserPlus className="h-4 w-4" />
          <span>Add User</span>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6 bg-card">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-medium">Total Users</h3>
          </div>
          <p className="text-3xl font-bold">573</p>
          <p className="text-sm text-muted-foreground mt-1">+5% from last month</p>
        </Card>
        
        <Card className="p-6 bg-card">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-medium">Active Users</h3>
          </div>
          <p className="text-3xl font-bold">542</p>
          <p className="text-sm text-muted-foreground mt-1">94.6% of total users</p>
        </Card>
        
        <Card className="p-6 bg-card">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-medium">New Signups</h3>
          </div>
          <p className="text-3xl font-bold">28</p>
          <p className="text-sm text-muted-foreground mt-1">This week</p>
        </Card>
      </div>

      <Card className="bg-card">
        <div className="p-4 border-b border-neutral-800 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search users..."
              className="w-full pl-10 pr-4 py-2 bg-background border border-input rounded-md focus:outline-none focus:ring-1 focus:ring-ring"
            />
          </div>
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            <Filter className="h-4 w-4" />
            <span>Filter</span>
          </Button>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left text-xs text-muted-foreground border-b border-neutral-800">
                <th className="px-4 py-3">Name</th>
                <th className="px-4 py-3">Email</th>
                <th className="px-4 py-3">Role</th>
                <th className="px-4 py-3">Status</th>
                <th className="px-4 py-3">Last Login</th>
                <th className="px-4 py-3 text-right">Actions</th>
              </tr>
            </thead>
            <tbody>
              {users.map((user) => (
                <tr key={user.id} className="border-b border-neutral-800 last:border-0 hover:bg-muted/5">
                  <td className="px-4 py-3 text-sm font-medium">{user.name}</td>
                  <td className="px-4 py-3 text-sm">{user.email}</td>
                  <td className="px-4 py-3 text-sm">
                    <span className={`inline-block px-2 py-1 rounded-full text-xs ${
                      user.role === "Admin" 
                        ? "bg-purple-500/10 text-purple-500" 
                        : user.role === "Staff" 
                        ? "bg-blue-500/10 text-blue-500" 
                        : "bg-neutral-500/10 text-neutral-400"
                    }`}>
                      {user.role}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-sm">
                    <span className={`inline-block px-2 py-1 rounded-full text-xs ${
                      user.status === "Active" 
                        ? "bg-green-500/10 text-green-500" 
                        : user.status === "Inactive" 
                        ? "bg-neutral-500/10 text-neutral-400" 
                        : "bg-red-500/10 text-red-500"
                    }`}>
                      {user.status}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-sm">{user.lastLogin}</td>
                  <td className="px-4 py-3 text-sm text-right">
                    <div className="flex items-center justify-end space-x-2">
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Mail className="h-4 w-4" />
                        <span className="sr-only">Email</span>
                      </Button>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">More</span>
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className="p-4 border-t border-neutral-800 flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing <span className="font-medium">1</span> to <span className="font-medium">10</span> of <span className="font-medium">573</span> users
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" disabled>Previous</Button>
            <Button variant="outline" size="sm">Next</Button>
          </div>
        </div>
      </Card>
    </div>
  );
}
