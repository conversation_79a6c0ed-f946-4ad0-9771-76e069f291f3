import { createClient } from '@/utils/supabase/server';
import { db } from './index';
import { profiles } from './schema';
import { eq } from 'drizzle-orm';

export async function checkIsAdmin(userId: string): Promise<boolean> {
  try {
    const [profile] = await db
      .select({ isAdmin: profiles.isAdmin })
      .from(profiles)
      .where(eq(profiles.id, userId))
      .limit(1);
    
    return profile?.isAdmin || false;
  } catch (error) {
    return false;
  }
}

export async function getCurrentUser() {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  return user;
}

export async function requireAuth() {
  const user = await getCurrentUser();
  if (!user) {
    throw new Error('Unauthorized');
  }
  return user;
}

export async function requireAdmin() {
  const user = await requireAuth();
  const isAdmin = await checkIsAdmin(user.id);
  
  if (!isAdmin) {
    throw new Error('Forbidden: Admin access required');
  }
  
  return user;
}